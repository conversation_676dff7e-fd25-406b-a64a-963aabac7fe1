import Link from 'next/link'
import { NPISection, NPISectionHeader, NPISectionTitle, NPISectionDescription } from '@/components/ui/npi-section'
import { NPIButton } from '@/components/ui/npi-button'
import { ArrowLeft, Search } from 'lucide-react'

export default function ProjectNotFound() {
  return (
    <NPISection className="py-24 bg-[#FFFFFF] min-h-[70vh] flex items-center">
      <div className="container mx-auto px-4 text-center">
        <NPISectionHeader className="mb-12">
          <div className="text-6xl font-bold text-[#8A3E25] mb-6">404</div>
          <NPISectionTitle className="text-black mb-4">Project Not Found</NPISectionTitle>
          <NPISectionDescription className="text-[#725242] max-w-2xl mx-auto">
            The project you're looking for doesn't exist or may have been moved. 
            Please check the URL or browse our available projects.
          </NPISectionDescription>
        </NPISectionHeader>

        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <Link href="/projects">
            <NPIButton className="bg-[#8A3E25] hover:bg-[#25718A] text-white border-2 border-[#8A3E25] hover:border-[#25718A]">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Projects
            </NPIButton>
          </Link>
          <Link href="/">
            <NPIButton variant="outline" className="border-[#25718A] text-[#25718A] hover:bg-[#25718A] hover:text-white">
              <Search className="w-4 h-4 mr-2" />
              Explore Homepage
            </NPIButton>
          </Link>
        </div>
      </div>
    </NPISection>
  )
}
