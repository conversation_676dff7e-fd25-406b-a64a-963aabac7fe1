import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { NPIProjectDetailsClient } from './page.client'

interface ProjectPageProps {
  params: {
    id: string
  }
}

export async function generateMetadata({ params }: ProjectPageProps): Promise<Metadata> {
  try {
    // Fetch project data for metadata
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_SERVER_URL}/api/projects/${params.id}`,
      {
        cache: 'no-store',
      },
    )

    if (!response.ok) {
      return {
        title: 'Project Not Found | NPI',
        description: 'The requested project could not be found.',
      }
    }

    const data = await response.json()
    const project = data.project

    return {
      title: `${project.title} | NPI Projects`,
      description: project.summary || project.description || 'Learn more about this NPI project.',
      openGraph: {
        title: project.title,
        description: project.summary || project.description,
        images: project.image?.url ? [project.image.url] : [],
      },
    }
  } catch (error) {
    return {
      title: 'Project | NPI',
      description: 'Learn more about this NPI project.',
    }
  }
}

export default async function ProjectPage({ params }: ProjectPageProps) {
  try {
    // Fetch project data
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_SERVER_URL}/api/projects/${params.id}`,
      {
        cache: 'no-store',
      },
    )

    if (!response.ok) {
      notFound()
    }

    const data = await response.json()
    const project = data.project

    return <NPIProjectDetailsClient project={project} />
  } catch (error) {
    console.error('Error fetching project:', error)
    notFound()
  }
}
